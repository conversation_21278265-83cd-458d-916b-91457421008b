@extends('admin.layouts.app')
@section('title', "Chi tiết sản phẩm đã mua")
@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card custom-card shadow">
                <div class="card-header">
                    <h4 class="card-title">Thông tin sản phẩm</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.history.products.update', $order->id) }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-md-6 form-group mb-3">
                                <label for="username" class="form-label">T<PERSON><PERSON> kho<PERSON>n</label>
                                <input type="text" class="form-control" id="username" name="username"
                                       value="{{ $order->user ? $order->user->username : 'Người dùng đã bị xóa' }}" readonly>
                                @if(!$order->user)
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        T<PERSON><PERSON> khoản này đã bị xóa nhưng thông tin đơn hàng được giữ lại
                                    </small>
                                @endif
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="product" class="form-label">Sản phẩm</label>
                                <input type="text" class="form-control" id="product" name="product"
                                       value="{{ $order->product ? $order->product->name : 'Sản phẩm đã bị xóa' }}" readonly>
                                @if(!$order->product)
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Sản phẩm này đã bị xóa
                                    </small>
                                @endif
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="quantity" class="form-label">Số lượng</label>
                                <input type="text" class="form-control" id="quantity" name="quantity" value="{{ $order->quantity }}" readonly>
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="status" class="form-label">Trạng thái</label>
                                <select class="form-select" id="status" name="status" readonly>
                                    <option value="success">Thành công</option>
                                </select>
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="price" class="form-label">Giá</label>
                                <input type="text" class="form-control" id="price" name="price" value="{{ number_format($order->price) }}đ" readonly>
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="total" class="form-label">Tổng tiền</label>
                                <input type="text" class="form-control" id="total" name="total" value="{{ number_format($order->payment) }}đ" readonly>
                            </div>
                            <div class="col-md-12 form-group mb-3">
                                @php
                                    $userData = $order->data ?? '';
                                    $isDeleted = str_contains($order->note ?? '', 'Dữ liệu đã được xóa bởi user');

                                    // Lấy dữ liệu gốc từ backup trong note
                                    $adminData = $userData;
                                    if ($isDeleted && preg_match('/ADMIN_DATA_BACKUP: ([A-Za-z0-9+\/=]+)/', $order->note ?? '', $matches)) {
                                        $adminData = base64_decode($matches[1]);
                                    }

                                    // Lấy thời gian xóa
                                    $deletedTime = '';
                                    if ($isDeleted && preg_match('/Dữ liệu đã được xóa bởi user lúc ([^]]+)\]/', $order->note ?? '', $matches)) {
                                        $deletedTime = $matches[1];
                                    }
                                @endphp

                                <label for="data" class="form-label">
                                    Dữ liệu sản phẩm (Admin View)
                                    @if($isDeleted)
                                        <span class="badge bg-warning ms-2">
                                            <i class="fas fa-exclamation-triangle"></i> User đã xóa
                                            @if($deletedTime)
                                                - {{ $deletedTime }}
                                            @endif
                                        </span>
                                    @elseif(!empty($adminData))
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-check-circle"></i> Dữ liệu gốc
                                        </span>
                                    @else
                                        <span class="badge bg-secondary ms-2">
                                            <i class="fas fa-times-circle"></i> Không có dữ liệu
                                        </span>
                                    @endif
                                </label>

                                <textarea class="form-control" id="data" name="data" rows="5"
                                          placeholder="@if(empty($adminData))Không có dữ liệu sản phẩm@endif">{{ $adminData }}</textarea>

                                @if($isDeleted)
                                    <div class="alert alert-warning mt-2">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Lưu ý:</strong> User đã xóa dữ liệu này khỏi tài khoản của họ,
                                        nhưng admin vẫn có thể xem dữ liệu gốc để phục vụ công tác quản lý.
                                    </div>
                                @endif

                                @if($isDeleted && !empty($userData))
                                    <div class="mt-3">
                                        <label class="form-label">Dữ liệu hiện tại của User:</label>
                                        <textarea class="form-control" rows="3" readonly>{{ $userData }}</textarea>
                                    </div>
                                @endif
                            </div>
                            <div class="col-md-12 form-group mb-3">
                                <label for="note" class="form-label">Ghi chú</label>
                                <textarea class="form-control" id="note" name="note" rows="5">{{ $order->note }}</textarea>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
