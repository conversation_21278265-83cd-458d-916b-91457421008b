<?php

namespace App\Listeners;

use App\Mail\LoginNotification;
use Illuminate\Auth\Events\Login;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendLoginNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        $user = $event->user;

        // Kiểm tra cấu hình SMTP có đầy đủ không
        if (siteValue('smtp_host') != null &&
            siteValue('smtp_port') != null &&
            siteValue('smtp_user') != null &&
            siteValue('smtp_pass') != null &&
            siteValue('smtp_name') != null) {

            Mail::to($user->email)->send(new LoginNotification($user));
        }
    }
}
