<?php

namespace App\Http\Controllers\Guard;

use App\Http\Controllers\Controller;
use App\Library\TelegramCustom;
use App\Models\OrderProduct;
use App\Models\PartnerWebsite;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductInventory;
use App\Models\ProductMain;
use App\Models\Transaction;
use App\Library\DiscordSdk;
use App\Models\Site;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use GuzzleHttp\Client;



class ClientProductController extends Controller
{
    public function CronJobProduct(Request $request)
    {
        $products = Product::where('type','taphoammo')->where('domain',env("APP_MAIN_SITE"))->get();
        foreach($products as $product)
        {
            $curl = curl_init();

            curl_setopt_array($curl, array(
              CURLOPT_URL => 'https://taphoammo.net/api/getStock?kioskToken='.$product->providerToken.'&userToken='.siteAdmin('api_token_taphoammo'),
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => '',
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 0,
              CURLOPT_FOLLOWLOCATION => true,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => 'GET',
            ));
            
            $response = curl_exec($curl);
            
            curl_close($curl);
            $data = json_decode($response, true);
              
            if(isset($data['success']) && $data['success'] === true || isset($data['price']))
            {
                if($product->rate !== $data['price'])
                {
                    $product->price = round(($data['price'] + ($data['price'] * siteValue('percent_product')/100)),0);
                    
                } 
                $product->rate = $data['price'];
                $product->stock = $data['stock'];
                $product->name = $data['name'];
                $product->save();   
                
            }
                     
        }
        
        
    } 
    public function CronJobProductChild(Request $request)
    {
        $products = Product::where('domain',env("APP_MAIN_SITE"))->get();
        foreach($products as $product)
        {
            $product_child = Product::where('child_id',$product->id)->where('domain',$request->getHost())->first();
              
            if(!$product_child)
            {
                $product_create_child = Product::create([
                    'product_main_id' => $product->product_main_id,
                    'name' => $product->name,
                    'child_id' => $product->id,
                    'price' => $product->price,
                    'rate' => $product->rate,
                    'stock' => $product->stock,
                    'status' => $product->status,
                    'order' => $product->order,
                    'type' => $product->type,
                    'providerToken' => $product->providerToken,
                    'domain' => $request->getHost()
                ]);
            }
            else{ 
                if($product->price !== $product_child['rate'])
                {
                    $product_child->price = round(($product['price'] + ($product['price'] * siteValue('percent_price_product')/100)),0);
                    
                } 
                $product_child->rate = $product['price'];
                $product_child->stock = $product['stock'];
                $product_child->name = $product['name'];
                $product_child->save();   
                
            }
                     
        }
        
        
    } 
    public function CategoriesView()
    {
        $products = ProductMain::where('status', 'active')->where('domain', env("APP_MAIN_SITE"))->orderBy('order', 'asc')->get();

        return view('guard.product.categories', compact('products'));
    }

    public function CategoryView($slug)
    {
        $category = ProductCategory::where('slug', $slug)->where('status', 'active')->where('domain', request()->getHost())->first();
        if (!$category) {
            return redirect()->route('guard.product.categories')->with('error', 'Danh mục không tồn tại');
        }

        $productsMain = $category->products()->where('status', 'active')->where('domain', request()->getHost())->orderBy('order', 'asc')->get();

        return view('guard.product.category', compact('category', 'productsMain'));
    }

    public function ProductView($slug)
    {
        $productMain = ProductMain::where('slug', $slug)->where('status', 'active')->where('domain', env("APP_MAIN_SITE"))->first();
        if (!$productMain) {
            return redirect()->route('guard.product.categories')->with('error', 'Sản phẩm không tồn tại');
        }

        // Tăng view count cho sản phẩm
        $productMain->increment('view');

        // Lấy sản phẩm gợi ý dựa trên số lượt mua cao nhất hoặc lượt xem cao nhất (top 5)
        // Ưu tiên theo số lượt mua, nếu bằng nhau thì theo lượt xem
        $suggestedProducts = ProductMain::where('status', 'active')
            ->where('domain', env("APP_MAIN_SITE"))
            ->where('id', '!=', $productMain->id) // Loại trừ sản phẩm hiện tại
            ->orderByDesc('sold') // Sắp xếp theo số lượt mua giảm dần
            ->orderByDesc('view') // Nếu sold bằng nhau thì sắp xếp theo lượt xem
            ->limit(5)
            ->get();

        return view('guard.product.index', compact('productMain', 'suggestedProducts'));
    }

    public function BoughtView()
    {
        $user = auth()->user();
        $orders = \App\Models\Order::where('user_id', $user->id)->get();
        return view('guard.product.bought', compact('orders', 'user'));
    }

    public function BuyProduct(Request $request)
    {
        $valid = Validator::make($request->all(), [
            'product_id' => 'required|integer',
            'quantity' => 'required|integer',
        ]);

        if ($valid->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => $valid->errors()->first()
            ]);
        }
        $api_token = $request->header('X-Access-Token');

        if (!$api_token) {
            return response()->json([
                'code' => '401',
                'status' => 'error',
                'message' => 'Không tìm thấy X-Access-Token !',
            ], 401);
        }

        

        if (request()->getHost() === env('APP_MAIN_SITE')) {
            $product = Product::where('id', $request->product_id)->where('domain', $request->getHost())->where('status', 'active')->first();
            if (!$product) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Sản phẩm không tồn tại'
                ]);
            }
    
            $total = $product->price * $request->quantity;
            $user = User::where('api_token', $api_token)->where('domain', getDomain())->first();
            if ($total > $user->balance) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Số dư không đủ để mua sản phẩm này'
                ]);
            }
            if ($product->type == 'manual') {

                $inventoryProduct = ProductInventory::where('product_id', $product->id)->where('status', 'available')->where('domain', env("APP_MAIN_SITE"))->limit($request->quantity)->get();

                // kiểm tra số lượng sản phẩm còn đủ không
                if (count($inventoryProduct) < $request->quantity) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Không đủ sản phẩm để mua'
                    ]);
                }

                $data = [];
                foreach ($inventoryProduct as $inventory) {
                    $inventory->status = 'sold';
                    $inventory->save();
                    $data[] = $inventory->data;
                }

                 
                $user->balance -= $total;
                $user->save();

                $orderProduct = OrderProduct::create([
                    'user_id' => $user->id,
                    'product_id' => $product->id,
                    'quantity' => $request->quantity,
                    'status' => 'success',
                    'price' => $product->price,
                    'rate' => $product->rate ?? '',
                    'payment' => $total,
                    'data' => implode(PHP_EOL, $data),
                    'note' => 'Mua sản phẩm ' . $product->name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'type' => 'product',
                    'providerToken' => $inventory->providerToken,
                    'domain' => $request->getHost(),
                ]);

                // Tăng sold count cho ProductMain
                $productMain = $product->productMain;
                if ($productMain) {
                    $productMain->increment('sold', $request->quantity);
                }

                $transaction = new Transaction();
                $transaction->user_id = $user->id;
                $transaction->tran_code = $orderProduct->id;
                $transaction->type = 'order';
                $transaction->action = 'sub';
                $transaction->before_balance = $user->balance + $total;
                $transaction->after_balance = $user->balance;
                $transaction->first_balance = $total;
                $transaction->ip = $request->ip();
                $transaction->note = 'Mua sản phẩm ' . $product->name . ' với giá ' . number_format($product->price) . ' x ' . $request->quantity;
                $transaction->domain = $request->getHost();
                $transaction->save();

                // Thông báo Discord cho đơn hàng sản phẩm manual
                if (siteValue('discord_webhook_product_url')) {
                    try {
                        $discord_notify = new DiscordSdk();
                        $discord_notify->botProduct()->sendMessage([
                            'text' => '🛒 **' . $request->getHost() . ' - Đơn hàng sản phẩm manual**' . PHP_EOL .
                                '👤 **Khách hàng:** ' . $user->name . ' (' . $user->email . ')' . PHP_EOL .
                                '📦 **Sản phẩm:** ' . $product->name . PHP_EOL .
                                '🔢 **Số lượng:** ' . number_format($request->quantity) . PHP_EOL .
                                '💰 **Giá tiền:** ' . number_format($product->price) . ' VNĐ/sp' . PHP_EOL .
                                '💵 **Tổng thanh toán:** ' . number_format($total) . ' VNĐ' . PHP_EOL .
                                '📧 **Email:** ' . ($request->email ?? 'Không có') . PHP_EOL .
                                '📱 **Phone:** ' . ($request->phone ?? 'Không có') . PHP_EOL .
                                '🕒 **Thời gian:** ' . now()->format('H:i:s d/m/Y') . PHP_EOL .
                                '🌐 **IP:** ' . $request->ip() . PHP_EOL,
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('Lỗi gửi thông báo Discord cho đơn hàng sản phẩm: ' . $e->getMessage());
                    }
                }

                return response()->json([
                    'status' => 'success',
                    'message' => 'Mua thành công!',
                    'total' => $total
                ]);
            }

            if ($product->type == 'taphoammo') {
                // kiểm tra số lượng sản phẩm còn đủ không
                if ($product->stock < $request->quantity) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Không đủ sản phẩm để mua'
                    ]);
                }
                $curl = curl_init();

                curl_setopt_array($curl, array(
                  CURLOPT_URL => 'https://taphoammo.net/api/buyProducts?kioskToken='.$product->providerToken.'&userToken='.siteAdmin('api_token_taphoammo').'&quantity='.$request->quantity,
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'GET',
                ));
                
                $response = curl_exec($curl);
                
                curl_close($curl);
                $data = json_decode($response, true);

                if (isset($data['success']) && $data['success'] === true || isset($data['order_id'])) {
                    
                    $user->balance -= $total;
                    $user->save();
                    

                    $orderProduct = OrderProduct::create([
                        'user_id' => $user->id,
                        'product_id' => $product->id,
                        'quantity' => $request->quantity,
                        'status' => 'success',
                        'price' => $product->price,
                        'rate' => $product->rate ?? '',
                        'payment' => $total,
                        'data' => '',
                        'note' => 'Mua sản phẩm ' . $product->name,
                        'email' => $request->email,
                        'phone' => $request->phone,
                        'type' => 'taphoammo',
                        'order_id' => $data['order_id'],
                        'providerToken' => $product->providerToken,
                        'domain' => $product->domain,
                    ]);

                    // Tăng sold count cho ProductMain
                    $productMain = $product->productMain;
                    if ($productMain) {
                        $productMain->increment('sold', $request->quantity);
                    }
                    
                    
                        $transaction = new Transaction();
                        $transaction->user_id = $user->id;
                        $transaction->tran_code = $orderProduct->id;
                        $transaction->type = 'order';
                        $transaction->action = 'sub';
                        $transaction->before_balance = $user->balance + $total;
                        $transaction->after_balance = $user->balance;
                        $transaction->first_balance = $total;
                        $transaction->ip = $request->ip();
                        $transaction->note = 'Mua sản phẩm ' . $product->name . ' với giá ' . number_format($product->price) . ' x ' . $request->quantity;
                        $transaction->domain = $request->getHost();
                        $transaction->save();
                        
                        // Thông báo Discord cho đơn hàng sản phẩm manual
                  if (siteValue('discord_webhook_product_url')) {
                    try {
                        $discord_notify = new DiscordSdk();
                        $discord_notify->botProduct()->sendMessage([
                            'text' => '🛒 **' . $request->getHost() . ' - Đơn hàng sản phẩm taphoammo**' . PHP_EOL .
                                '👤 **Khách hàng:** ' . $user->name . ' (' . $user->email . ')' . PHP_EOL .
                                '📦 **Sản phẩm:** ' . $product->name . PHP_EOL .
                                '🔢 **Số lượng:** ' . number_format($request->quantity) . PHP_EOL .
                                '💰 **Giá tiền:** ' . number_format($product->price) . ' VNĐ/sp' . PHP_EOL .
                                '💵 **Tổng thanh toán:** ' . number_format($total) . ' VNĐ' . PHP_EOL .
                                '📧 **Email:** ' . ($request->email ?? 'Không có') . PHP_EOL .
                                '📱 **Phone:** ' . ($request->phone ?? 'Không có') . PHP_EOL .
                                '🕒 **Thời gian:** ' . now()->format('H:i:s d/m/Y') . PHP_EOL .
                                '🌐 **IP:** ' . $request->ip() . PHP_EOL,
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('Lỗi gửi thông báo Discord cho đơn hàng sản phẩm: ' . $e->getMessage());
                    }
                 }

                    sleep(2);
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                      CURLOPT_URL => 'https://taphoammo.net/api/getProducts?orderId='.$data['order_id'].'&userToken='.siteAdmin('api_token_taphoammo'),
                      CURLOPT_RETURNTRANSFER => true,
                      CURLOPT_ENCODING => '',
                      CURLOPT_MAXREDIRS => 10,
                      CURLOPT_TIMEOUT => 0,
                      CURLOPT_FOLLOWLOCATION => true,
                      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                      CURLOPT_CUSTOMREQUEST => 'GET',
                    ));
                    
                    $response_order = curl_exec($curl);
                    
                    curl_close($curl);
                    $data_order = json_decode($response_order, true);
                    $orde = [];
                    if(isset($data_order['success']) && $data_order['success'] === true || isset($data_order['data']))
                    {
                        for ($i = 0; $i < $request->quantity; $i++) {
                            $data_orders = $data_order['data'][$i]['product'];
                            $orde[] = $data_orders;
                            
                        }
 
                    }
                    else{
                        $orde[] = "Đang chờ xử lý";
                    }
                    $orderProduct->data = implode("\n\n", $orde);
                    $orderProduct->save();
 
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Đã mua sản phẩm thành công',
                        'total' => $total
                    ]);
                } else {
                    return response()->json([
                        'status' => 'error',
                        'message' => $data['description'] ?? 'Lỗi vui lòng thử lại'
                    ]);
                }
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Sản phẩm không hỗ trợ mua vui lòng thử lại sau'
            ]);
        } else {
            $user = User::where('api_token', $api_token)->where('domain', getDomain())->first();
            $product = Product::where('id', $request->product_id)->where('domain', $user->domain)->where('status', 'active')->first();
            
            if (!$product) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Sản phẩm không tồn tại'
                ]);
            }
            $product_admin = Product::where('id', $product->child_id)->where('status', 'active')->first();
            if (!$product_admin) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Sản phẩm không tồn tại'
                ]);
            }
            $total = $product->price * $request->quantity;
            $total_admin = $product_admin->price * $request->quantity;
    
            if ($total > $user->balance) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Số dư không đủ để mua sản phẩm này'
                ]);
            }

            $partnerSite = PartnerWebsite::where('name', getDomain())->first();

            if (!$partnerSite) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Có lỗi xảy ra vui lòng thử lại sau 1'
                ]);
            }

            $admin = User::where('id', $partnerSite->user_id)->first();

            if (!$admin) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Có lỗi xảy ra vui lòng thử lại sau 2'
                ]);
            }

             

            if ($total_admin > $admin->balance) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Số dư ADMIN không đủ để mua sản phẩm này'
                ]);
            }
            if ($product->type == 'manual') {

                $inventoryProduct = ProductInventory::where('product_id', $product->id)->where('status', 'available')->where('domain', env("APP_MAIN_SITE"))->limit($request->quantity)->get();

                // kiểm tra số lượng sản phẩm còn đủ không
                if (count($inventoryProduct) < $request->quantity) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Không đủ sản phẩm để mua'
                    ]);
                }

                $data = [];
                foreach ($inventoryProduct as $inventory) {
                    $inventory->status = 'sold';
                    $inventory->save();
                    $data[] = $inventory->data;
                }
 
                $user->balance -= $total;
                $user->save();
                $admin->balance -= $total_admin;
                $admin->save();

                $orderProduct = OrderProduct::create([
                    'user_id' => $user->id,
                    'product_id' => $product->id,
                    'quantity' => $request->quantity,
                    'status' => 'success',
                    'price' => $product->price,
                    'rate' => $product->rate ?? '',
                    'payment' => $total,
                    'data' => implode(PHP_EOL, $data),
                    'note' => 'Mua sản phẩm ' . $product->name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'type' => 'product',
                    'providerToken' => $inventory->providerToken,
                    'domain' => $product->domain,
                ]);

                // Tăng sold count cho ProductMain
                $productMain = $product->productMain;
                if ($productMain) {
                    $productMain->increment('sold', $request->quantity);
                }

                $orderProductAdmin = OrderProduct::create([
                    'user_id' => $admin->id,
                    'product_id' => $product_admin->id,
                    'quantity' => $request->quantity,
                    'status' => 'success',
                    'price' => $product_admin->price,
                    'rate' => $product_admin->rate ?? '',
                    'payment' => $total_admin,
                    'data' => implode(PHP_EOL, $data),
                    'note' => 'Mua sản phẩm ' . $product_admin->name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'type' => 'product',
                    'providerToken' => $inventory->providerToken,
                    'domain' => $product_admin->domain,
                ]);

                // Thông báo Discord cho đơn hàng sản phẩm manual (kho tự thêm tay)
                if (siteValue('discord_webhook_product_url')) {
                    try {
                        $discord_notify = new DiscordSdk();
                        $discord_notify->botProduct()->sendMessage([
                            'text' => '🛒 **' . $request->getHost() . ' - Đơn hàng sản phẩm kho tự thêm tay**' . PHP_EOL .
                                '👤 **Khách hàng:** ' . $user->name . ' (' . $user->email . ')' . PHP_EOL .
                                '📦 **Sản phẩm:** ' . $product->name . PHP_EOL .
                                '🔢 **Số lượng:** ' . number_format($request->quantity) . PHP_EOL .
                                '💰 **Giá tiền:** ' . number_format($product->price) . ' VNĐ/sp' . PHP_EOL .
                                '💵 **Tổng thanh toán:** ' . number_format($total) . ' VNĐ' . PHP_EOL .
                                '📧 **Email:** ' . ($request->email ?? 'Không có') . PHP_EOL .
                                '📱 **Phone:** ' . ($request->phone ?? 'Không có') . PHP_EOL .
                                '🕒 **Thời gian:** ' . now()->format('H:i:s d/m/Y') . PHP_EOL .
                                '🌐 **IP:** ' . $request->ip() . PHP_EOL,
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('Lỗi gửi thông báo Discord cho đơn hàng sản phẩm manual: ' . $e->getMessage());
                    }
                }

                $transaction = new Transaction();
                $transaction->user_id = $user->id;
                $transaction->tran_code = $orderProduct->id;
                $transaction->type = 'order';
                $transaction->action = 'sub';
                $transaction->before_balance = $user->balance + $total;
                $transaction->after_balance = $user->balance;
                $transaction->first_balance = $total;
                $transaction->ip = $request->ip();
                $transaction->note = 'Mua sản phẩm ' . $product->name . ' với giá ' . number_format($product->price) . ' x ' . $request->quantity;
                $transaction->domain = $product->domain;
                $transaction->save();

                $transaction_admin = new Transaction();
                $transaction_admin->user_id = $admin->id;
                $transaction_admin->tran_code = $orderProductAdmin->id;
                $transaction_admin->type = 'order';
                $transaction_admin->action = 'sub';
                $transaction_admin->before_balance = $admin->balance + $total_admin;
                $transaction_admin->after_balance = $admin->balance;
                $transaction_admin->first_balance = $total_admin;
                $transaction_admin->ip = $request->ip();
                $transaction_admin->note = 'Mua sản phẩm ' . $product_admin->name . ' với giá ' . number_format($product_admin->price) . ' x ' . $request->quantity;
                $transaction_admin->domain = $product_admin->domain;
                $transaction_admin->save();

 

                return response()->json([
                    'status' => 'success',
                    'message' => 'Mua thành công!',
                    'total' => $total
                ]);
            }

            if ($product->type == 'taphoammo') {
                // kiểm tra số lượng sản phẩm còn đủ không
                if ($product->stock < $request->quantity) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Không đủ sản phẩm để mua'
                    ]);
                }
                $curl = curl_init();

                curl_setopt_array($curl, array(
                  CURLOPT_URL => 'https://taphoammo.net/api/buyProducts?kioskToken='.$product->providerToken.'&userToken='.siteAdmin('api_token_taphoammo').'&quantity='.$request->quantity,
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'GET',
                ));
                
                $response = curl_exec($curl);
                
                curl_close($curl);
                $data = json_decode($response, true);

                if (isset($data['success']) && $data['success'] === true || isset($data['order_id'])) {
                    
                    $user->balance -= $total;
                    $user->save();
                    $admin->balance -= $total;
                    $admin->save();
                    

                    $orderProduct = OrderProduct::create([
                        'user_id' => $user->id,
                        'product_id' => $product->id,
                        'quantity' => $request->quantity,
                        'status' => 'success',
                        'price' => $product->price,
                        'rate' => $product->rate ?? '',
                        'payment' => $total,
                        'data' => '',
                        'note' => 'Mua sản phẩm ' . $product->name,
                        'email' => $request->email,
                        'phone' => $request->phone,
                        'type' => 'taphoammo',
                        'order_id' => $data['order_id'],
                        'providerToken' => $product->providerToken,
                        'domain' => $request->getHost(),
                    ]);

                    // Tăng sold count cho ProductMain
                    $productMain = $product->productMain;
                    if ($productMain) {
                        $productMain->increment('sold', $request->quantity);
                    }
                    $orderProductAdmin = OrderProduct::create([
                        'user_id' => $admin->id,
                        'product_id' => $product_admin->id,
                        'quantity' => $request->quantity,
                        'status' => 'success',
                        'price' => $product_admin->price,
                        'rate' => $product_admin->rate ?? '',
                        'payment' => $total_admin,
                        'data' => '',
                        'note' => 'Mua sản phẩm ' . $product_admin->name,
                        'email' => $request->email,
                        'phone' => $request->phone,
                        'type' => 'taphoammo',
                        'order_id' => $data['order_id'],
                        'providerToken' => $product_admin->providerToken,
                        'domain' => $admin->domain,
                    ]);
                    
                    
                    $transaction = new Transaction();
                    $transaction->user_id = $user->id;
                    $transaction->tran_code = $orderProduct->id;
                    $transaction->type = 'order';
                    $transaction->action = 'sub';
                    $transaction->before_balance = $user->balance + $total;
                    $transaction->after_balance = $user->balance;
                    $transaction->first_balance = $total;
                    $transaction->ip = $request->ip();
                    $transaction->note = 'Mua sản phẩm ' . $product->name . ' với giá ' . number_format($product->price) . ' x ' . $request->quantity;
                    $transaction->domain = $request->getHost();
                    $transaction->save();

                    $transaction_admin = new Transaction();
                    $transaction_admin->user_id = $admin->id;
                    $transaction_admin->tran_code = $orderProduct->id;
                    $transaction_admin->type = 'order';
                    $transaction_admin->action = 'sub';
                    $transaction_admin->before_balance = $admin->balance + $total_admin;
                    $transaction_admin->after_balance = $admin->balance;
                    $transaction_admin->first_balance = $total_admin;
                    $transaction_admin->ip = $request->ip();
                    $transaction_admin->note = 'Mua sản phẩm ' . $product_admin->name . ' với giá ' . number_format($product_admin->price) . ' x ' . $request->quantity;
                    $transaction_admin->domain = $admin->domain;
                    $transaction_admin->save();
                    
                    
                    

                    sleep(1);
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                      CURLOPT_URL => 'https://taphoammo.net/api/getProducts?orderId='.$data['order_id'].'&userToken='.siteAdmin('api_token_taphoammo'),
                      CURLOPT_RETURNTRANSFER => true,
                      CURLOPT_ENCODING => '',
                      CURLOPT_MAXREDIRS => 10,
                      CURLOPT_TIMEOUT => 0,
                      CURLOPT_FOLLOWLOCATION => true,
                      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                      CURLOPT_CUSTOMREQUEST => 'GET',
                    ));
                    
                    $response_order = curl_exec($curl);
                    
                    curl_close($curl);
                    $data_order = json_decode($response_order, true);
                    $orde = [];
                    if(isset($data_order['success']) && $data_order['success'] === true || isset($data_order['data']))
                    {
                        for ($i = 0; $i < $request->quantity; $i++) {
                            $data_orders = $data_order['data'][$i]['product'];
                            $orde[] = $data_orders;
                            
                        }
 
                    }
                    else{
                        $orde[] = "Đang chờ xử lý";
                    }
                    $orderProduct->data = implode("\n\n", $orde);
                    $orderProduct->save();
                    $orderProductAdmin->data = implode("\n\n", $orde);
                    $orderProductAdmin->save();

                    // Thông báo Discord cho đơn hàng sản phẩm từ taphoammo
                    if (siteValue('discord_webhook_product_url')) {
                        try {
                            $discord_notify = new DiscordSdk();
                            $discord_notify->botProduct()->sendMessage([
                                'text' => '🛒 **' . $request->getHost() . ' - Đơn hàng sản phẩm từ TapHoaMmo**' . PHP_EOL .
                                    '👤 Khách hàng: ' . $user->name . ' (' . $user->email . ')' . PHP_EOL .
                                    '📦 Sản phẩm:' . $product->name . PHP_EOL .
                                    '🔢 Số lượng: ' . number_format($request->quantity) . PHP_EOL .
                                    '💰 Giá tiền:' . number_format($product->price) . ' VNĐ/sp' . PHP_EOL .
                                    '💵 Tổng thanh toán: ' . number_format($total) . ' VNĐ' . PHP_EOL .
                                    '🆔 Order ID: ' . $data['order_id'] . PHP_EOL .
                                    '📧Email: ' . ($request->email ?? 'Không có') . PHP_EOL .
                                    '📱 Phone: ' . ($request->phone ?? 'Không có') . PHP_EOL .
                                    '🕒 Thời gian: ' . now()->format('H:i:s d/m/Y') . PHP_EOL .
                                    '🌐 IP: ' . $request->ip() . PHP_EOL,
                            ]);
                        } catch (\Exception $e) {
                            \Log::error('Lỗi gửi thông báo Discord cho đơn hàng taphoammo: ' . $e->getMessage());
                        }
                    }

                    return response()->json([
                        'status' => 'success',
                        'message' => 'Đã mua sản phẩm thành công',
                        'total' => $total
                    ]);
                } else {
                    return response()->json([
                        'status' => 'error',
                        'message' => $data['description'] ?? 'Lỗi vui lòng thử lại'
                    ]);
                }
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Sản phẩm không hỗ trợ mua vui lòng thử lại sau'
            ]);
        }
    }

    public function DownloadProduct(Request $request, $id)
    {
        try {
            // Validate ID
            if (!is_numeric($id) || $id <= 0) {
                return redirect()->back()->with('error', 'ID đơn hàng không hợp lệ');
            }

            $orderProduct = OrderProduct::where('id', $id)
                ->where('user_id', $user->id)
                ->where('status', 'success')
                ->where('domain', request()->getHost())
                ->first();

            if (!$orderProduct) {
                return redirect()->back()->with('error', 'Đơn hàng không tồn tại hoặc chưa thành công');
            }

            if (empty($orderProduct->data)) {
                return redirect()->back()->with('error', 'Không có dữ liệu để tải xuống');
            }

            $productName = $orderProduct->product->name ?? 'product';
            $fileName = 'product_' . $id . '_' . date('Y-m-d_H-i-s') . '.txt';

            $headers = [
                'Content-Type' => 'text/plain; charset=utf-8',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ];

            return response($orderProduct->data, 200, $headers);

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Có lỗi xảy ra khi tải xuống: ' . $e->getMessage());
        }
    }

    public function DeleteProductData(Request $request, $id)
    {
        try {
            // Validate ID
            if (!is_numeric($id) || $id <= 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID đơn hàng không hợp lệ'
                ]);
            }

            $orderProduct = OrderProduct::where('id', $id)
                ->where('user_id', $user->id)
                ->where('domain', request()->getHost())
                ->first();

            if (!$orderProduct) {
                return response()->json([
                    'success' => false,
                    'message' => 'Đơn hàng không tồn tại'
                ]);
            }

            // Kiểm tra xem có dữ liệu để xóa không
            if (empty($orderProduct->data)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không có dữ liệu để xóa'
                ]);
            }

            // Chỉ xóa dữ liệu, không xóa đơn hàng
            $orderProduct->data = '';
            $orderProduct->save();

            return response()->json([
                'success' => true,
                'message' => 'Đã xóa dữ liệu sản phẩm thành công'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }
}
