@extends('admin.layouts.app')
@section('title', 'Quản lý công việc tự động')
@section('content')
<div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><PERSON>h sách công việc tự động</h5>
                <a href="{{ route('admin.automation.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Thêm công việc
                </a>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tên công việc</th>
                                <th>Loại công việc</th>
                                <th>Hoàn thành sau (ngày)</th>
                                <th>Thực hiện mỗi (phút)</th>
                                <th>Đã xóa</th>
                                <th>Trạng thái</th>
                                <th>Lần chạy cuối</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($automations as $automation)
                                <tr>
                                    <td>{{ $automation->id }}</td>
                                    <td>{{ $automation->name }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ $automation->type_name }}</span>
                                    </td>
                                    <td>{{ $automation->completed_days }}</td>
                                    <td>
                                        @php
                                            $minutes = $automation->execution_minutes;
                                            if ($minutes >= 1440) {
                                                $days = floor($minutes / 1440);
                                                $hours = floor(($minutes % 1440) / 60);
                                                $mins = $minutes % 60;
                                                $timeText = $days . ' ngày';
                                                if ($hours > 0) $timeText .= ' ' . $hours . ' giờ';
                                                if ($mins > 0) $timeText .= ' ' . $mins . ' phút';
                                            } elseif ($minutes >= 60) {
                                                $hours = floor($minutes / 60);
                                                $mins = $minutes % 60;
                                                $timeText = $hours . ' giờ';
                                                if ($mins > 0) $timeText .= ' ' . $mins . ' phút';
                                            } else {
                                                $timeText = $minutes . ' phút';
                                            }
                                        @endphp
                                        <span class="badge bg-secondary">{{ $timeText }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ number_format($automation->total_deleted) }} bản ghi</span>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" 
                                                   type="checkbox" 
                                                   data-id="{{ $automation->id }}"
                                                   {{ $automation->status == 'active' ? 'checked' : '' }}>
                                            <label class="form-check-label">
                                                {{ $automation->status == 'active' ? 'Hoạt động' : 'Tạm dừng' }}
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        @if($automation->last_run)
                                            {{ $automation->last_run->format('d/m/Y H:i') }}
                                        @else
                                            <span class="text-muted">Chưa chạy</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" 
                                                    class="btn btn-sm btn-success run-manual" 
                                                    data-id="{{ $automation->id }}"
                                                    title="Chạy thủ công">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button type="button" 
                                                    class="btn btn-sm btn-danger delete-automation" 
                                                    data-id="{{ $automation->id }}"
                                                    title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9" class="text-center">
                                        <div class="py-4">
                                            <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Chưa có công việc tự động nào</p>
                                            <a href="{{ route('admin.automation.create') }}" class="btn btn-primary">
                                                Tạo công việc đầu tiên
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal xác nhận xóa -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Bạn có chắc chắn muốn xóa công việc tự động này?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
$(document).ready(function() {
    // Toggle trạng thái
    $('.status-toggle').change(function() {
        const id = $(this).data('id');
        const status = $(this).is(':checked') ? 'active' : 'inactive';
        const label = $(this).next('label');
        
        $.ajax({
            url: `/admin/automation/${id}/status`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                status: status
            },
            success: function(response) {
                label.text(status === 'active' ? 'Hoạt động' : 'Tạm dừng');
                toastr.success('Cập nhật trạng thái thành công!');
            },
            error: function() {
                toastr.error('Có lỗi xảy ra!');
                // Revert toggle
                $(this).prop('checked', !$(this).is(':checked'));
            }
        });
    });

    // Chạy thủ công
    $('.run-manual').click(function() {
        const id = $(this).data('id');
        const btn = $(this);
        
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
        
        $.ajax({
            url: `/admin/automation/${id}/run`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                toastr.success(response.message);
                location.reload();
            },
            error: function() {
                toastr.error('Có lỗi xảy ra!');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-play"></i>');
            }
        });
    });

    // Xóa automation
    $('.delete-automation').click(function() {
        const id = $(this).data('id');
        $('#deleteForm').attr('action', `/admin/automation/${id}`);
        $('#deleteModal').modal('show');
    });
});
</script>
@endsection
