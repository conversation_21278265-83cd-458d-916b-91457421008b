<?php

namespace App\Http\Controllers\Api\Order;

use App\Http\Controllers\Api\SocialProviders\TuongtaccheoController;
use App\Http\Controllers\Api\SocialProviders\TraodoisubController;

use App\Http\Controllers\Controller;
use App\Library\TelegramSdk;
use App\Models\Order;
use App\Models\Smm;
use App\Models\Voucher;
use App\Models\ChildPanel;
use App\Models\ServerAction;
use App\Models\Service;
use App\Models\ServiceServer;
use App\Models\ServicePlatform;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ActionOrderController extends Controller
{
    public function refundOrder(Request $request)
    {
        try {
            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token !',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ !',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này !',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'order_code' => 'required',
            ], [
                'order_code.required' => 'Vui lòng nhập mã đơn hàng cần hoàn tiền !',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            } else {
                $order = Order::where('order_code', $request->order_code)->where('user_id', $user->id)->where('domain', $domain)->first();
                if (!$order) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy đơn hàng cần hoàn tiền !',
                    ], 400);
                }

                if ($order->status === 'Refunded') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã được hoàn tiền trước đó !',
                    ], 400);
                }

                if ($order->status === 'WaitingForRefund') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đang chờ hoàn tiền !',
                    ], 400);
                }

                if ($order->status === 'Completed') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã hoàn thành không thể hoàn tiền !',
                    ], 400);
                }

                if ($order->status === 'Cancelled') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã bị hủy không thể hoàn tiền !',
                    ], 400);
                }

                if ($order->status === 'Failed') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã thất bại không thể hoàn tiền !',
                    ], 400);
                }

                if ($order->status === 'Partially Refunded') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã được hoàn tiền một phần không thể hoàn tiền !',
                    ], 400);
                }

                if ($order->status === 'Partially Completed') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã hoàn thành một phần không thể hoàn tiền !',
                    ], 400);
                }

                $server = $order->server;
                if ($server) {

                    $action = $server->action;
                    if (!$action) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Không tìm thấy thông tin máy chủ !',
                        ], 400);
                    }

                    if ($action->refund_status !== 'on') {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Máy chủ này không hỗ trợ hoàn tiền !',
                        ], 400);
                    }

                    if ($server->providerName == 'dontay') {

                        if ($server->action->time_status == 'on') {
                            $order->remaining = 0;
                            $order->status = 'Refunded';
                            $order->save();

                            // send notify telegram
                            if (site('telegram_bot_token') && site('telegram_chat_id')) {
                                try {
                                    $bot_notify = new TelegramSdk();
                                    $bot_notify->botNotify()->sendMessage([
                                        'chat_id' => site('telegram_chat_id'),
                                        'text' => '🛒 <b>Đơn hàng đã được hoàn tiền từ website ' . $domain . ' !' . "</b>\n\n" .
                                            '👤 <b>Khách hàng:</b> ' . $user->name . ' (' . $user->email . ')' . "\n" .
                                            '📦 <b>Gói dịch vụ:</b> ' . $order->service->package . "\n" .
                                            '🔗 <b>Link hoặc UID:</b> ' . $order->object_id . "\n" .
                                            '🔢 <b>Số lượng:</b> ' . number_format($order->quantity) . "\n" .
                                            '🔗 <b>Máy chủ:</b> ' . $server->package_id . "\n" .
                                            '💰 <b>Giá tiền:</b> ' . $order->price . 'đ' . "\n" .
                                            '💵 <b>Thanh toán:</b> ' . $order->payment . 'đ' . "\n" .
                                            '📝 <b>Ghi chú:</b> ' . $order->note . "\n",
                                        'parse_mode' => 'HTML',
                                    ]);
                                } catch (\Exception $e) {


                                }
                            }


                            return response()->json([
                                'code' => '200',
                                'status' => 'success',
                                'message' => 'Đơn hàng của bạn đã được hoàn tiền !',
                            ], 200);
                        } else {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => 'Đơn hàng này không hỗ trợ hủy hoàn, trân trọng!',
                            ], 400);
                        }
                    } else {


                        ###smm
                        $tran = Transaction::where('tran_code', $order->order_id)->first();

                        if ($tran) {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => 'Đơn hàng đang trong quá trình hủy vui lòng chờ !',
                            ], 400);
                        }
                        if ($user->balance < 1000) {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => 'Tài khoản của bạn không đủ 1000 vnd để thực hiện yêu cầu hủy !',
                            ], 400);
                        }
                        $smms = Smm::where('domain', env('APP_MAIN_SITE'))->where('name', $order->orderProviderName)->first();


                        $path = $smms->name;

                        $post = array(
                            'key' => $smms->token,
                            'action' => 'cancel',

                            'order' => $order->order_id

                        );
                        $result = curl_smm($path, $post);
                        if (isset($result['cancel']) && !empty($result['cancel'])) {


                            $transaction = new Transaction();
                            $transaction->user_id = $user->id;
                            $transaction->tran_code = $order->order_id;
                            $transaction->type = 'refund';
                            $transaction->action = 'sub';
                            $transaction->first_balance = 1000;
                            $transaction->before_balance = $user->balance;
                            $transaction->after_balance = $user->balance - 1000;
                            $transaction->note = 'Thực hiện thao tác hủy hoàn mã đơn ' . $order->order_code . ' với chi phí hủy là 1000 vnđ';
                            $transaction->ip = $request->ip();
                            $transaction->domain = $domain;
                            $transaction->save();

                            $user->balance = $user->balance - 1000;
                            $user->save();
                            return response()->json([
                                'code' => '200',
                                'status' => 'success',
                                'message' => 'Đơn hàng của bạn đã được gửi yêu cầu hủy hoàn!',
                            ], 200);


                        } else {


                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => 'Đơn hàng này không hỗ trợ hủy hoàn, trân trọng!',
                            ], 400);
                        }
                    }

                } else {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy máy chủ của đơn hàng này !',
                    ], 400);
                }
            }
        } catch (\Exception $e) {
            return response()->json([
                'code' => '500',
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }


    public function upOrder(Request $request)
    {
        try {
            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token !',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ !',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này !',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'order_code' => 'required',
            ], [
                'order_code.required' => 'Vui lòng nhập mã đơn hàng cần hoàn tiền !',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            } else {
                $order = Order::where('order_code', $request->order_code)->where('user_id', $user->id)->where('domain', $domain)->first();
                if (!$order) {
                    return response()->json(['code' => '400', 'status' => 'error', 'message' => 'Không tìm thấy đơn hàng!',], 400);
                }

                $server = $order->server;
                if ($server) {

                    $action = $server->action;
                    if (!$action) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Không tìm thấy thông tin máy chủ!',
                        ], 400);
                    }

                    if ($action->up_status !== 'on') {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Máy chủ này không hỗ trợ tăng tốc!',
                        ], 400);
                    }
                    if ($order->status !='Running') {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Chỉ đơn hàng đang chạy mới được tăng tốc!',
                        ], 400);
                    }
    
                    if ($order->status === 'Cancelled') {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Đơn hàng này đã bị hủy không thể gia hạn!',
                        ], 400);
                    }
    

                    if ($server->providerName == 'tuongtaccheo') {
                        if ($user->balance < 1000) {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => 'Tài khoản của bạn không đủ 1000 vnd để thực hiện yêu cầu tăng tốc !',
                            ], 400);
                        }
                        
                        $ttc = new TuongtaccheoController();
                        $ttc->path = $server->providerLink;
                        $item = json_decode($order->order_data);
                        $maluc = ($item->quantity - $order->buff) * $server->price_speed_up;
                        $result = $ttc->order_up($order->order_id,$maluc);
                        if (isset($result['status']) && $result['status'] == true) {
                            $user->balance = $user->balance - 1000;
                            $user->save();
                            $transaction = new Transaction();
                            $transaction->user_id = $user->id;
                            $transaction->tran_code = $order->order_id;
                            $transaction->type = 'refund';
                            $transaction->action = 'sub';
                            $transaction->first_balance = 1000;
                            $transaction->before_balance = $user->balance;
                            $transaction->after_balance = $user->balance - 1000;
                            $transaction->note = 'Thực hiện thao tác tăng tốc mã đơn ' . $order->order_code . ' với chi phí hủy là 1000 vnđ';
                            $transaction->ip = $request->ip();
                            $transaction->domain = $domain;
                            $transaction->save();
                            return response()->json([
                                'code' => '200',
                                'status' => 'success',
                                'message' => 'Đơn hàng của bạn đã được tăng tốc !',
                            ], 200);
                        } else {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => 'Có lỗi xảy ra',
                            ], 400);
                        }
                    } else {
                        
                    }

                } else {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy máy chủ của đơn hàng này !',
                    ], 400);
                }
            }
        } catch (\Exception $e) {
            return response()->json([
                'code' => '500',
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function warrantyOrder(Request $request)
    {
        try {
            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token !',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ !',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này !',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'order_code' => 'required',
            ], [
                'order_code.required' => 'Vui lòng nhập mã đơn hàng cần bảo hành !',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            } else {
                $order = Order::where('order_code', $request->order_code)->where('user_id', $user->id)->where('domain', $domain)->first();
                if (!$order) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy đơn hàng cần bảo hành !',
                    ], 400);
                }

                if ($order->status === 'Refunded') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã được hoàn tiền không thể bảo hành !',
                    ], 400);
                }

                

                $server = $order->server;
                if ($server) {
                        $tran = Transaction::where('tran_code', $order->order_id)->first();

                        if ($tran) {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => 'Đơn hàng đang trong quá trình bảo hành vui lòng chờ !',
                            ], 400);
                        }
                         
                        $smms = Smm::where('domain', env('APP_MAIN_SITE'))->where('name', $order->orderProviderName)->first();


                        $path = $smms->name;

                        $post = array(
                            'key' => $smms->token,
                            'action' => 'refill',

                            'order' => $order->order_id

                        );
                        $result = curl_smm($path, $post);
                        if (isset($result['refill']) && !empty($result['refill'])) {


                            $transaction = new Transaction();
                            $transaction->user_id = $user->id;
                            $transaction->tran_code = $order->order_id;
                            $transaction->type = 'refund';
                            $transaction->action = 'sub';
                            $transaction->first_balance = 0;
                            $transaction->before_balance = $user->balance;
                            $transaction->after_balance = $user->balance - 0;
                            $transaction->note = 'Thực hiện thao tác bảo hành mã đơn ' . $order->order_code;
                            $transaction->ip = $request->ip();
                            $transaction->domain = $domain;
                            $transaction->save();

                         
                            $user->save();
                            return response()->json([
                                'code' => '200',
                                'status' => 'success',
                                'message' => 'Đơn hàng của bạn đã được gửi yêu cầu bảo hành!',
                            ], 200);


                        } else {


                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => 'Đơn hàng này không hỗ trợ bảo hành, trân trọng!',
                            ], 400);
                        
                    }
                } else {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy máy chủ của đơn hàng này !',
                    ], 400);
                }
            }
        } catch (\Exception $e) {
            return response()->json([
                'code' => '500',
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function updateOrder(Request $request)
    {
        try {
            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token !',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ !',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này !',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'order_code' => 'required',
            ], [
                'order_code.required' => 'Vui lòng nhập mã đơn hàng cần cập nhật !',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            } else {
                $order = Order::where('order_code', $request->order_code)->where('user_id', $user->id)->where('domain', $domain)->first();
                if (!$order) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy đơn hàng cần cập nhật !',
                    ], 400);
                }

                if ($order->status === 'Completed') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã hoàn thành không thể cập nhật !',
                    ], 400);
                }

                if ($order->status === 'Cancelled') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã bị hủy không thể cập nhật !',
                    ], 400);
                }

                if ($order->status === 'Failed') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã thất bại không thể cập nhật !',
                    ], 400);
                }

                if ($order->status === 'Refunded') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã được hoàn tiền không thể cập nhật !',
                    ], 400);
                }

                if ($order->status === 'WaitingForRefund') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đang chờ hoàn tiền không thể cập nhật !',
                    ], 400);
                }

                if ($order->status === 'Partially Refunded') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã được hoàn tiền một phần không thể cập nhật !',
                    ], 400);
                }

                if ($order->status === 'Partially Completed') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã hoàn thành một phần không thể cập nhật !',
                    ], 400);
                }

                $server = $order->server;
                if ($server) {
                    if ($order->orderProviderName == 'traodoisub') {



                        $tds = new TraodoisubController();

                        $tds->path = $order['orderProviderPath'];
                        $result = $tds->order($order['order_code']);

                        if (isset($result['status']) && $result['status'] == true) {
                            if (isset($result['data']['data'])) {
                                foreach ($result['data']['data'] as $data) {
                                    if (isset($data['note']) && $data['note'] == $order['order_code']) {



                                        $code_order = $data['note'];
                                        $status = $data['status'];

                                        if (isset($data['start'])) {

                                            $start = $data['start'];
                                        } else {
                                            $start = 0;
                                        }
                                        $item = json_decode($order->order_data);
                                        $buff = $item->quantity - ($data['sl'] - $data['datang']);
                                        $order = Order::where('order_code', $code_order)->first();
                                        if ($order) {
                                            switch ($status) {
                                                case '<span class="badge badge badge-soft-success">Đang Chạy</span>':
                                                    $order->start = $start;
                                                    $order->buff = $buff;
                                                    $order->status = 'Running';
                                                    break;
                                                case '<span class="badge badge badge-soft-primary">Hoàn Thành</span>':
                                                    $order->start = $start;
                                                    $order->buff = $buff;
                                                    $order->status = 'Completed';
                                                    break;
                                                case 'Report':
                                                    $order->start = $start;
                                                    $order->buff = $buff;
                                                    $order->status = 'Failed';
                                                    break;
                                                case '<span class="badge badge badge-soft-warning">Tạm dừng</span>':
                                                    $order->start = $start;
                                                    $order->buff = $buff;
                                                    $order->status = 'Holding';
                                                    break;
                                                case 'Error':
                                                    $order->start = $start;
                                                    $order->buff = $buff;
                                                    $order->status = 'Failed';
                                                    break;
                                                case 'Refund':
                                                    $order->start = $start;
                                                    $order->buff = $buff;
                                                    $order->status = 'Refunded';

                                                    break;
                                                default:
                                                    $order->start = $start;
                                                    $order->buff = $buff;
                                                    $order->status = 'Running';
                                                    break;
                                            }
                                            $order->save();
                                            return response()->json([
                                                'code' => '200',
                                                'status' => 'success',
                                                'message' => 'Đơn hàng của bạn đã được cập nhật thành công !',
                                            ], 200);
                                        }
                                    }
                                }
                            }


                        } else {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => $result['message'],
                            ], 400);
                        }
                    } else {
                        //  cập nhật thành công
                        return response()->json([
                            'code' => '200',
                            'status' => 'success',
                            'message' => 'Đơn hàng của bạn đã được cập nhật thành công !',
                        ], 200);
                    }
                } else {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy máy chủ của đơn hàng này !',
                    ], 400);
                }
            }
        } catch (\Exception $e) {
            return response()->json([
                'code' => '500',
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function renewOrder(Request $request)
    {
        try {
            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token !',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ !',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này !',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'order_code' => 'required',
                'days' => 'required|numeric|min:1'
            ], [
                'order_code.required' => 'Vui lòng nhập mã đơn hàng cần cập nhật !',
                'days.required' => 'Vui lòng nhập số ngày gia hạn !',
                'days.numeric' => 'Số ngày gia hạn phải là số !',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            } else {
                $order = Order::where('order_code', $request->order_code)->where('user_id', $user->id)->where('domain', $domain)->first();
                if (!$order) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy đơn hàng cần cập nhật !',
                    ], 400);
                }

                if ($order->status === 'Cancelled') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã bị hủy không thể gia hạn !',
                    ], 400);
                }

                if ($order->status === 'Failed') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Đơn hàng này đã thất bại không thể gia hạn !',
                    ], 400);
                }

                $server = $order->server;
                if ($server) {

                    $price = $order->price;
                    $payment = $order->payment;
                    $quantity = $order->orderdata()['quantity'];
                    $duration = $order->orderdata()['duration'];
                    $posts = $order->orderdata()['posts'] === 'unlimited' ? 1 : $order->orderdata()['posts'];
                    $total = $price * $request->days * $quantity * $posts;

                    if ($user->balance < ceil($total)) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Số dư của bạn không đủ để thực hiện giao dịch này !',
                        ], 400);
                    }

                    $orderdata = json_decode($order->order_data);
                    $orderdata->posts = $orderdata->posts === 'unlimited' ? 1 : $orderdata->posts;
                    $orderdata->payment = $orderdata->payment + ceil($total);
                    $order->order_data = json_encode($orderdata);
                    $order->status = 'Processing';
                    $order->payment = ceil($total);
                    $order->remaining = $order->remaining + $request->days;
                    $order->time = now();
                    $order->save();

                    $tranCode = 'R_' . time() . rand(1000, 9999);
                    Transaction::create([
                        'user_id' => $order->user_id,
                        'tran_code' => $tranCode,
                        'type' => 'renew',
                        'action' => 'sub',
                        'first_balance' => ceil($total),
                        'before_balance' => $order->user->balance,
                        'after_balance' => $order->user->balance - ceil($total),
                        'note' => 'Gia hạn đơn hàng #' . $order->order_code,
                        'ip' => $request->ip(),
                        'domain' => $order->domain,
                    ]);

                    $order->user->balance -= ceil($total);
                    $order->user->save();

                    if (site('telegram_bot_token') && site('telegram_chat_id')) {
                        $bot_notify = new TelegramSdk();
                        $bot_notify->botNotify()->sendMessage([
                            'chat_id' => site('telegram_chat_id'),
                            'text' => "Đơn hàng <b>#{$order->order_code}</b> đã được gia hạn thêm <b>{$request->days}</b> ngày với giá <b>" . number_format(ceil($total)) . "đ</b>",
                            'parse_mode' => 'HTML',
                        ]);
                    }

                    return response()->json([
                        'code' => '200',
                        'status' => 'success',
                        'message' => 'Đơn hàng của bạn đã được gia hạn thành công !',
                    ], 200);
                } else {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy máy chủ của đơn hàng này !',
                    ], 400);
                }
            }
        } catch (\Exception $e) {
            return response()->json([
                'code' => '500',
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
