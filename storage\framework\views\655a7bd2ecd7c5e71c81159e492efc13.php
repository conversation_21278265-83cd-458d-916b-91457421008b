<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thông báo đăng nhập thành công</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .title {
            color: #17a2b8;
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
        }
        .content {
            margin-bottom: 30px;
        }
        .login-info {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }
        .login-info h3 {
            margin-top: 0;
            color: #17a2b8;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #bee5eb;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .label {
            font-weight: bold;
            color: #495057;
        }
        .value {
            color: #17a2b8;
            font-weight: 500;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #17a2b8;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .button:hover {
            background-color: #138496;
        }
        .login-icon {
            font-size: 48px;
            color: #17a2b8;
            text-align: center;
            margin-bottom: 20px;
        }
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .security-notice h4 {
            color: #856404;
            margin-top: 0;
        }
        .security-notice p {
            color: #856404;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo"><?php echo e(siteValue('name_site') ?? 'Website'); ?></div>
            <div class="login-icon">🔐</div>
        </div>

        <h1 class="title">Thông báo đăng nhập thành công</h1>

        <div class="content">
            <p>Xin chào <strong><?php echo e($user->name); ?></strong>,</p>
            
            <p>Chúng tôi ghi nhận bạn vừa đăng nhập thành công vào tài khoản của mình.</p>

            <div class="login-info">
                <h3>Chi tiết phiên đăng nhập:</h3>
                <div class="info-row">
                    <span class="label">Tên đăng nhập:</span>
                    <span class="value"><?php echo e($user->username); ?></span>
                </div>
                <div class="info-row">
                    <span class="label">Email:</span>
                    <span class="value"><?php echo e($user->email); ?></span>
                </div>
                <div class="info-row">
                    <span class="label">Thời gian đăng nhập:</span>
                    <span class="value"><?php echo e(now()->format('d/m/Y H:i:s')); ?></span>
                </div>
                <div class="info-row">
                    <span class="label">Địa chỉ IP:</span>
                    <span class="value"><?php echo e(request()->ip()); ?></span>
                </div>
                <div class="info-row">
                    <span class="label">Trình duyệt:</span>
                    <span class="value"><?php echo e(request()->userAgent()); ?></span>
                </div>
            </div>

            <div class="security-notice">
                <h4>⚠️ Thông báo bảo mật</h4>
                <p>Nếu bạn không thực hiện đăng nhập này, vui lòng liên hệ với chúng tôi ngay lập tức để bảo vệ tài khoản của bạn.</p>
            </div>

            <div style="text-align: center;">
                <a href="<?php echo e(url('/')); ?>" class="button">Truy cập tài khoản</a>
            </div>

            <p><strong>Khuyến nghị bảo mật:</strong></p>
            <ul>
                <li>Luôn đăng xuất sau khi sử dụng xong</li>
                <li>Không sử dụng tài khoản trên máy tính công cộng</li>
                <li>Thay đổi mật khẩu định kỳ</li>
                <li>Kích hoạt xác thực 2 bước nếu có thể</li>
            </ul>
        </div>

        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống <?php echo e(siteValue('name_site') ?? 'Website'); ?></p>
            <p>Vui lòng không trả lời email này.</p>
            <p>© <?php echo e(date('Y')); ?> <?php echo e(siteValue('name_site') ?? 'Website'); ?>. Tất cả quyền được bảo lưu.</p>
        </div>
    </div>
</body>
</html>
<?php /**PATH /home/<USER>/public_html/resources/views/emails/login_notification.blade.php ENDPATH**/ ?>