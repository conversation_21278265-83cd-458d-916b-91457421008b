
<?php $__env->startSection('title', "Chi tiết sản phẩm đã mua"); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card custom-card shadow">
                <div class="card-header">
                    <h4 class="card-title">Thông tin sản phẩm</h4>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.history.products.update', $order->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-md-6 form-group mb-3">
                                <label for="username" class="form-label">Tài khoản</label>
                                <input type="text" class="form-control" id="username" name="username"
                                       value="<?php echo e($order->user ? $order->user->username : 'Người dùng đã bị xóa'); ?>" readonly>
                                <?php if(!$order->user): ?>
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Tài khoản này đã bị xóa nhưng thông tin đơn hàng được giữ lại
                                    </small>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="product" class="form-label">Sản phẩm</label>
                                <input type="text" class="form-control" id="product" name="product"
                                       value="<?php echo e($order->product ? $order->product->name : 'Sản phẩm đã bị xóa'); ?>" readonly>
                                <?php if(!$order->product): ?>
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Sản phẩm này đã bị xóa
                                    </small>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="quantity" class="form-label">Số lượng</label>
                                <input type="text" class="form-control" id="quantity" name="quantity" value="<?php echo e($order->quantity); ?>" readonly>
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="status" class="form-label">Trạng thái</label>
                                <select class="form-select" id="status" name="status" readonly>
                                    <option value="success">Thành công</option>
                                </select>
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="price" class="form-label">Giá</label>
                                <input type="text" class="form-control" id="price" name="price" value="<?php echo e(number_format($order->price)); ?>đ" readonly>
                            </div>
                            <div class="col-md-6 form-group mb-3">
                                <label for="total" class="form-label">Tổng tiền</label>
                                <input type="text" class="form-control" id="total" name="total" value="<?php echo e(number_format($order->payment)); ?>đ" readonly>
                            </div>
                            <div class="col-md-12 form-group mb-3">
                                <?php
                                    $userData = $order->data ?? '';
                                    $isDeleted = str_contains($order->note ?? '', 'Dữ liệu đã được xóa bởi user');

                                    // Lấy dữ liệu gốc từ backup trong note
                                    $adminData = $userData;
                                    if ($isDeleted && preg_match('/ADMIN_DATA_BACKUP: ([A-Za-z0-9+\/=]+)/', $order->note ?? '', $matches)) {
                                        $adminData = base64_decode($matches[1]);
                                    }

                                    // Lấy thời gian xóa
                                    $deletedTime = '';
                                    if ($isDeleted && preg_match('/Dữ liệu đã được xóa bởi user lúc ([^]]+)\]/', $order->note ?? '', $matches)) {
                                        $deletedTime = $matches[1];
                                    }
                                ?>

                                <label for="data" class="form-label">
                                    Dữ liệu sản phẩm (Admin View)
                                    <?php if($isDeleted): ?>
                                        <span class="badge bg-warning ms-2">
                                            <i class="fas fa-exclamation-triangle"></i> User đã xóa
                                            <?php if($deletedTime): ?>
                                                - <?php echo e($deletedTime); ?>

                                            <?php endif; ?>
                                        </span>
                                    <?php elseif(!empty($adminData)): ?>
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-check-circle"></i> Dữ liệu gốc
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary ms-2">
                                            <i class="fas fa-times-circle"></i> Không có dữ liệu
                                        </span>
                                    <?php endif; ?>
                                </label>

                                <textarea class="form-control" id="data" name="data" rows="5"
                                          placeholder="<?php if(empty($adminData)): ?>Không có dữ liệu sản phẩm@endif"><?php echo e($adminData); ?></textarea>

                                <?php if($isDeleted): ?>
                                    <div class="alert alert-warning mt-2">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Lưu ý:</strong> User đã xóa dữ liệu này khỏi tài khoản của họ,
                                        nhưng admin vẫn có thể xem dữ liệu gốc để phục vụ công tác quản lý.
                                    </div>
                                <?php endif; ?>

                                <?php if($isDeleted && !empty($userData)): ?>
                                    <div class="mt-3">
                                        <label class="form-label">Dữ liệu hiện tại của User:</label>
                                        <textarea class="form-control" rows="3" readonly><?php echo e($userData); ?></textarea>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-12 form-group mb-3">
                                <label for="note" class="form-label">Ghi chú</label>
                                <textarea class="form-control" id="note" name="note" rows="5"><?php echo e($order->note); ?></textarea>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/history/product-detail.blade.php ENDPATH**/ ?>