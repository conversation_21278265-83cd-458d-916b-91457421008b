<?php $__env->startSection('title', 'Notification | E-Mail | SMTP'); ?>
<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title">Notification | E-Mail | SMTP</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('admin.smtp.update')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                       placeholder="smtp.gmail.com" value="<?php echo e(siteValue('smtp_host')); ?>">
                                <label for="smtp_host">SMTP Host</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                       placeholder="587" value="<?php echo e(siteValue('smtp_port')); ?>">
                                <label for="smtp_port">SMTP Port</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="smtp_user" name="smtp_user"
                                       placeholder="<EMAIL>" value="<?php echo e(siteValue('smtp_user')); ?>">
                                <label for="smtp_user">SMTP Username</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="smtp_pass" name="smtp_pass"
                                       placeholder="••••••••" value="<?php echo e(siteValue('smtp_pass')); ?>">
                                <label for="smtp_pass">SMTP Password</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="smtp_name" name="smtp_name"
                                       placeholder="Website Name" value="<?php echo e(siteValue('smtp_name')); ?>">
                                <label for="smtp_name">From Name</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Lưu cấu hình
                                </button>
                                <button type="button" class="btn btn-info" id="testEmailBtn">
                                    <i class="fas fa-paper-plane me-2"></i>Gửi email thử nghiệm
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>

<script>
document.getElementById('testEmailBtn').addEventListener('click', function() {
    const host = document.getElementById('smtp_host').value;
    const port = document.getElementById('smtp_port').value;
    const user = document.getElementById('smtp_user').value;
    const pass = document.getElementById('smtp_pass').value;
    const name = document.getElementById('smtp_name').value;

    if (!host || !port || !user || !pass || !name) {
        alert('Vui lòng điền đầy đủ thông tin SMTP!');
        return;
    }

    if (confirm('Gửi email thử nghiệm đến: ' + user + '?')) {
        fetch('<?php echo e(route("admin.smtp.test")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({
                smtp_host: host,
                smtp_port: port,
                smtp_user: user,
                smtp_pass: pass,
                smtp_name: name
            })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.success ? 'Email gửi thành công!' : 'Lỗi: ' + data.message);
        })
        .catch(error => {
            alert('Lỗi: ' + error.message);
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Src Khách\maxsocial\resources\views/admin/smtp.blade.php ENDPATH**/ ?>