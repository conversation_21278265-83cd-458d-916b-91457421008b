<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Automation;
use App\Models\Order;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Recharge;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AutomationController extends Controller
{
    /**
     * Hi<PERSON>n thị danh sách automation
     */
    public function index()
    {
        $domain = request()->getHost();
        $automations = Automation::where('domain', $domain)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.automation.index', compact('automations'));
    }

    /**
     * <PERSON><PERSON>n thị form tạo automation
     */
    public function create()
    {
        $types = Automation::TYPES;
        return view('admin.automation.create', compact('types'));
    }

    /**
     * Lưu automation mới
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:' . implode(',', array_keys(Automation::TYPES)),
            'completed_days' => 'required|integer|min:1',
            'execution_minutes' => 'required|integer|min:1',
        ]);

        $domain = request()->getHost();

        Automation::create([
            'name' => $request->name,
            'type' => $request->type,
            'completed_days' => $request->completed_days,
            'execution_minutes' => $request->execution_minutes,
            'status' => 'active',
            'domain' => $domain,
        ]);

        return redirect()->route('admin.automation.index')
            ->with('success', 'Tạo công việc tự động thành công!');
    }

    /**
     * Cập nhật trạng thái automation
     */
    public function updateStatus(Request $request, $id)
    {
        $automation = Automation::findOrFail($id);
        $automation->update([
            'status' => $request->status
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Xóa automation
     */
    public function destroy($id)
    {
        $automation = Automation::findOrFail($id);
        $automation->delete();

        return redirect()->route('admin.automation.index')
            ->with('success', 'Xóa công việc tự động thành công!');
    }

    /**
     * Chạy automation thủ công
     */
    public function runManual($id)
    {
        $automation = Automation::findOrFail($id);
        $result = $this->executeAutomation($automation);

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'deleted_count' => $result['deleted_count']
        ]);
    }

    /**
     * API endpoint để chạy tất cả automation (dùng cho cron job)
     */
    public function runAll()
    {
        $domain = request()->getHost();
        $automations = Automation::where('domain', $domain)
            ->where('status', 'active')
            ->get();

        $results = [];
        foreach ($automations as $automation) {
            if ($automation->shouldRun()) {
                $result = $this->executeAutomation($automation);
                $results[] = [
                    'automation' => $automation->name,
                    'result' => $result
                ];
            }
        }

        return response()->json([
            'success' => true,
            'results' => $results
        ]);
    }

    /**
     * Thực thi automation
     */
    private function executeAutomation(Automation $automation)
    {
        $domain = $automation->domain;
        $completedDays = $automation->completed_days;
        $cutoffDate = Carbon::now()->subDays($completedDays);
        $deletedCount = 0;

        try {
            DB::beginTransaction();

            switch ($automation->type) {
                case 'delete_completed_orders':
                    $deletedCount = $this->deleteCompletedOrders($domain, $cutoffDate);
                    break;

                case 'delete_completed_recharges':
                    $deletedCount = $this->deleteCompletedRecharges($domain, $cutoffDate);
                    break;

                case 'delete_completed_transactions':
                    $deletedCount = $this->deleteCompletedTransactions($domain, $cutoffDate);
                    break;

                case 'delete_inactive_users':
                    $deletedCount = $this->deleteInactiveUsers($domain, $cutoffDate);
                    break;
            }

            // Cập nhật thời gian chạy cuối cùng và tổng số đã xóa
            $automation->update([
                'last_run' => Carbon::now(),
                'total_deleted' => $automation->total_deleted + $deletedCount
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => "Đã xóa {$deletedCount} bản ghi",
                'deleted_count' => $deletedCount
            ];

        } catch (\Exception $e) {
            DB::rollback();
            return [
                'success' => false,
                'message' => 'Lỗi: ' . $e->getMessage(),
                'deleted_count' => 0
            ];
        }
    }

    /**
     * Xóa đơn hàng đã hoàn thành
     */
    private function deleteCompletedOrders($domain, $cutoffDate)
    {
        return Order::where('domain', $domain)
            ->where('status', 'Completed')
            ->where('updated_at', '<', $cutoffDate)
            ->delete();
    }

    /**
     * Xóa lịch sử nạp tiền đã hoàn thành
     */
    private function deleteCompletedRecharges($domain, $cutoffDate)
    {
        if (class_exists('App\Models\Recharge')) {
            return Recharge::where('domain', $domain)
                ->where('status', 'Success')
                ->where('updated_at', '<', $cutoffDate)
                ->delete();
        }
        return 0;
    }

    /**
     * Xóa dòng tiền đã xuất hiện
     */
    private function deleteCompletedTransactions($domain, $cutoffDate)
    {
        return Transaction::where('domain', $domain)
            ->where('created_at', '<', $cutoffDate)
            ->delete();
    }

    /**
     * Xóa user không nạp tiền và không sử dụng dịch vụ
     */
    private function deleteInactiveUsers($domain, $cutoffDate)
    {
        // Tìm user không có transaction nạp tiền và không có order
        $inactiveUsers = User::where('domain', $domain)
            ->where('created_at', '<', $cutoffDate)
            ->where('role', 'member') // Chỉ xóa member, không xóa admin
            ->whereDoesntHave('transactions', function($query) {
                $query->whereIn('type', ['recharge', 'balance'])
                    ->where('action', 'add');
            })
            ->whereDoesntHave('orders')
            ->get();

        $count = $inactiveUsers->count();
        
        foreach ($inactiveUsers as $user) {
            $user->delete();
        }

        return $count;
    }
}
