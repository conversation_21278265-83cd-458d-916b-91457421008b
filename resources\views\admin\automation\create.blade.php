@extends('admin.layouts.app')
@section('title', 'Thêm công việc tự động')
@section('content')
<div class="row">
    <div class="col-xl-8 mx-auto">
        <div class="card custom-card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thêm công việc tự động</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.automation.store') }}" method="POST">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Tên công việc <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control @error('name') is-invalid @enderror" 
                               id="name" 
                               name="name" 
                               value="{{ old('name') }}"
                               placeholder="Nhập tên công việc tự động">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="type" class="form-label">Loại công việc <span class="text-danger">*</span></label>
                        <select class="form-select @error('type') is-invalid @enderror" id="type" name="type">
                            <option value="">Chọn loại công việc</option>
                            @foreach($types as $key => $value)
                                <option value="{{ $key }}" {{ old('type') == $key ? 'selected' : '' }}>
                                    {{ $value }}
                                </option>
                            @endforeach
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="completed_days" class="form-label">Thời gian đã hoàn thành <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" 
                                   class="form-control @error('completed_days') is-invalid @enderror" 
                                   id="completed_days" 
                                   name="completed_days" 
                                   value="{{ old('completed_days') }}"
                                   min="1"
                                   placeholder="Nhập số ngày">
                            <span class="input-group-text">ngày</span>
                        </div>
                        <div class="form-text">Xóa dữ liệu đã hoàn thành được bao nhiêu ngày</div>
                        @error('completed_days')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        
                        <!-- Quick select buttons -->
                        <div class="mt-2">
                            <small class="text-muted">Chọn nhanh:</small>
                            <div class="btn-group btn-group-sm mt-1" role="group">
                                <button type="button" class="btn btn-outline-secondary quick-select" data-target="completed_days" data-value="1">1 ngày</button>
                                <button type="button" class="btn btn-outline-secondary quick-select" data-target="completed_days" data-value="3">3 ngày</button>
                                <button type="button" class="btn btn-outline-secondary quick-select" data-target="completed_days" data-value="7">7 ngày</button>
                                <button type="button" class="btn btn-outline-secondary quick-select" data-target="completed_days" data-value="30">30 ngày</button>
                                <button type="button" class="btn btn-outline-secondary quick-select" data-target="completed_days" data-value="90">90 ngày</button>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="execution_minutes" class="form-label">Thời gian thực hiện <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number"
                                   class="form-control @error('execution_minutes') is-invalid @enderror"
                                   id="execution_minutes"
                                   name="execution_minutes"
                                   value="{{ old('execution_minutes') }}"
                                   min="1"
                                   placeholder="Nhập số phút">
                            <span class="input-group-text">phút</span>
                        </div>
                        <div class="form-text">Thực hiện công việc này mỗi bao nhiêu phút</div>
                        @error('execution_minutes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror

                        <!-- Quick select buttons -->
                        <div class="mt-2">
                            <small class="text-muted">Chọn nhanh:</small>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="btn-group btn-group-sm mt-1" role="group">
                                        <button type="button" class="btn btn-outline-secondary quick-select" data-target="execution_minutes" data-value="5">5 phút</button>
                                        <button type="button" class="btn btn-outline-secondary quick-select" data-target="execution_minutes" data-value="15">15 phút</button>
                                        <button type="button" class="btn btn-outline-secondary quick-select" data-target="execution_minutes" data-value="30">30 phút</button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="btn-group btn-group-sm mt-1" role="group">
                                        <button type="button" class="btn btn-outline-secondary quick-select" data-target="execution_minutes" data-value="60">1 giờ</button>
                                        <button type="button" class="btn btn-outline-secondary quick-select" data-target="execution_minutes" data-value="1440">1 ngày</button>
                                        <button type="button" class="btn btn-outline-secondary quick-select" data-target="execution_minutes" data-value="10080">1 tuần</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Mô tả các loại công việc:</h6>
                        <ul class="mb-0">
                            <li><strong>Xóa đơn hàng đã hoàn thành:</strong> Xóa các đơn hàng có trạng thái "Completed"</li>
                            <li><strong>Xóa lịch sử nạp tiền đã hoàn thành:</strong> Xóa các giao dịch nạp tiền có trạng thái "Success"</li>
                            <li><strong>Xóa dòng tiền đã xuất hiện:</strong> Xóa các bản ghi transaction cũ</li>
                            <li><strong>Xóa user không hoạt động:</strong> Xóa user không nạp tiền và không có đơn hàng nào</li>
                        </ul>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.automation.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Tạo công việc
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
$(document).ready(function() {
    // Quick select buttons
    $('.quick-select').click(function() {
        const target = $(this).data('target');
        const value = $(this).data('value');
        $(`#${target}`).val(value);
        
        // Remove active class from siblings and add to current
        $(this).siblings().removeClass('active');
        $(this).addClass('active');
    });
    
    // Form validation
    $('form').submit(function(e) {
        let isValid = true;
        
        // Check required fields
        $('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            toastr.error('Vui lòng điền đầy đủ thông tin!');
        }
    });
});
</script>
@endsection
