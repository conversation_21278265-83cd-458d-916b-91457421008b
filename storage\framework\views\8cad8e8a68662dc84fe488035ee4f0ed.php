<?php $__env->startSection('title', 'Thêm sản phẩm'); ?>
<?php $__env->startSection('content'); ?>
    <div class="card custom-card shadow">
        <div class="card-header">
            <h4 class="card-title">Thêm sản phẩm</h4>
        </div>
        <div class="card-body">
            <form action="<?php echo e(route('admin.products.store')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="form-group mb-3">
                    <label for="name" class="form-label">Tên sản phẩm</label>
                    <input type="text" class="form-control" id="name" name="name" placeholder="Nhập tên sản phẩm"
                        value="<?php echo e(old('name')); ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="description" class="form-label">Ghi chú</label>
                    <textarea class="form-control" id="description" name="description" rows="3" placeholder="Nhập ghi chú"><?php echo old('description'); ?></textarea>
                </div>
                <div class="form-group mb-3">
                    <label for="image" class="form-label">Hình ảnh</label>
                    <input type="file" class="form-control" id="image" name="image">
                </div>
                <div class="form-group mb-3">
                    <label for="slug" class="form-label">Đường dẫn</label>
                    <input type="text" class="form-control" id="slug" name="slug" placeholder="Nhập đường dẫn"
                        value="<?php echo e(old('slug')); ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="order" class="form-label">Thứ tự</label>
                    <input type="number" class="form-control" id="order" name="order" placeholder="Nhập thứ tự"
                        value="<?php echo e(old('order')); ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="status" class="form-label">Trạng thái</label>
                    <select class="form-control" id="status" name="status">
                        <option value="active" <?php echo e(old('status') == 'active' ? 'selected' : ''); ?>>Hiển thị</option>
                        <option value="inactive" <?php echo e(old('status') == 'inactive' ? 'selected' : ''); ?>>Ẩn</option>
                    </select>
                </div>
                <div class="row">
                    <div class="col-md-6 form-group mb-3">
                        <label for="is_email" class="form-label">Email</label>
                        <select class="form-control" id="is_email" name="is_email">
                            <option value="0" <?php echo e(old('is_email') == 0 ? 'selected' : ''); ?>>Không</option>
                            <option value="1" <?php echo e(old('is_email') == 1 ? 'selected' : ''); ?>>Có</option>
                        </select>
                    </div>
                    <div class="col-md-6 form-group mb-3">
                        <label for="is_phone" class="form-label">Số điện thoại</label>
                        <select class="form-control" id="is_phone" name="is_phone">
                            <option value="0" <?php echo e(old('is_phone') == 0 ? 'selected' : ''); ?>>Không</option>
                            <option value="1" <?php echo e(old('is_phone') == 1 ? 'selected' : ''); ?>>Có</option>
                        </select>
                    </div>
                </div>
                <h4 class="fs-4 mb-3">Meta SEO</h4>
                <div class="form-group mb-3">
                    <label for="meta_title" class="form-label">Tiêu đề</label>
                    <input type="text" class="form-control" id="meta_title" name="meta_title" placeholder="Nhập tiêu đề"
                        value="<?php echo e(old('meta_title')); ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="meta_description" class="form-label">Nội dung</label>
                    <textarea class="form-control" id="meta_description" name="meta_description" rows="3" placeholder="Nhập nội dung"><?php echo old('meta_description'); ?></textarea>
                </div>
                <div class="form-group mb-3">
                    <label for="meta_keywords" class="form-label">Từ khoá</label>
                    <input type="text" class="form-control" id="meta_keywords" name="meta_keywords"
                        placeholder="Nhập từ khoá" value="<?php echo e(old('meta_keywords')); ?>">
                </div>
                <button type="submit" class="btn btn-primary">Thêm mới</button>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    <script>
        CKEDITOR.replace('description');
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Src Khách\maxsocial\resources\views/admin/product/product-create.blade.php ENDPATH**/ ?>