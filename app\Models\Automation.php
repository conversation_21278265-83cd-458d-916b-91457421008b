<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Automation extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'completed_days',
        'execution_minutes',
        'total_deleted',
        'status',
        'last_run',
        'domain',
    ];

    protected $casts = [
        'last_run' => 'datetime',
    ];

    // Các loại automation có thể thực hiện
    const TYPES = [
        'delete_completed_orders' => 'Xóa đơn hàng đã hoàn thành',
        'delete_completed_recharges' => 'Xóa lịch sử nạp tiền đã hoàn thành',
        'delete_completed_transactions' => 'Xóa dòng tiền đã xuất hiện',
        'delete_inactive_users' => 'Xóa user không nạp tiền và không sử dụng dịch vụ',
    ];

    // <PERSON><PERSON><PERSON> tra xem automation có cần chạy không
    public function shouldRun()
    {
        if ($this->status !== 'active') {
            return false;
        }

        if (!$this->last_run) {
            return true;
        }

        $nextRun = $this->last_run->addMinutes($this->execution_minutes);
        return Carbon::now()->gte($nextRun);
    }

    // Lấy tên hiển thị của loại automation
    public function getTypeNameAttribute()
    {
        return self::TYPES[$this->type] ?? $this->type;
    }
}
