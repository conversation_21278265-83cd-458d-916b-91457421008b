<?php $__env->startSection('title', 'Hỗ Trợ Ticket'); ?>
<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">Danh Sách Ticket Hỗ Trợ</h5>
                    <div class="d-flex gap-2">
                        <span class="badge bg-warning">Chờ xử lý: <?php echo e($ticket->where('status', 'pending')->count()); ?></span>
                        <span class="badge bg-info">Đang xử lý: <?php echo e($ticket->where('status', 'processing')->count()); ?></span>
                        <span class="badge bg-success">Đã xử lý: <?php echo e($ticket->where('status', 'success')->count()); ?></span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Filter Form -->
                <form action="" method="GET" class="mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group mb-3">
                                <input type="text" class="form-control" name="search"
                                    placeholder="Tìm kiếm theo tiêu đề, nội dung, ID..."
                                    value="<?php echo e(request('search')); ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group mb-3">
                                <select class="form-select" name="category">
                                    <option value="">Tất cả danh mục</option>
                                    <option value="don_hang" <?php echo e(request('category') == 'don_hang' ? 'selected' : ''); ?>>Đơn hàng</option>
                                    <option value="thanh_toan" <?php echo e(request('category') == 'thanh_toan' ? 'selected' : ''); ?>>Thanh toán</option>
                                    <option value="dich_vu" <?php echo e(request('category') == 'dich_vu' ? 'selected' : ''); ?>>Dịch vụ</option>
                                    <option value="webcon" <?php echo e(request('category') == 'webcon' ? 'selected' : ''); ?>>Webcon</option>
                                    <option value="khac" <?php echo e(request('category') == 'khac' ? 'selected' : ''); ?>>Khác</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group mb-3">
                                <select class="form-select" name="status">
                                    <option value="">Tất cả trạng thái</option>
                                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Chờ xử lý</option>
                                    <option value="processing" <?php echo e(request('status') == 'processing' ? 'selected' : ''); ?>>Đang xử lý</option>
                                    <option value="success" <?php echo e(request('status') == 'success' ? 'selected' : ''); ?>>Đã xử lý</option>
                                    <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Đã hủy</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-search"></i> Tìm kiếm
                                </button>
                                <a href="<?php echo e(route('admin.ticket.ticket')); ?>" class="btn btn-secondary">
                                    <i class="ti ti-refresh"></i> Reset
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table text-nowrap table-striped table-hover table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Thao Tác</th>
                                <th>Người tạo</th>
                                <th>Danh mục</th>
                                <th>Tiêu đề</th>
                                <th>Trạng thái</th>
                                <th>Thời gian tạo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $ticket; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tickets): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($tickets->id); ?></td>
                                <td>
                                    <a href="<?php echo e(route('admin.ticket.ticket.edit', ['id' => $tickets->id])); ?>"
                                        class="btn btn-sm btn-success"
                                        data-bs-toggle="tooltip" title="Xem Chi Tiết">
                                        <i class="ti ti-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.ticket.ticket.delete', ['id' => $tickets->id])); ?>"
                                        class="btn btn-sm btn-danger"
                                        data-bs-toggle="tooltip" title="Xóa"
                                        onclick="return confirm('Bạn có chắc chắn muốn xóa ticket này?')">
                                        <i class="ti ti-trash"></i>
                                    </a>
                                </td>
                                <td>
                                    <strong><?php echo e($tickets->user->name ?? 'N/A'); ?></strong><br>
                                    <small class="text-muted"><?php echo e($tickets->user->email ?? 'N/A'); ?></small>
                                </td>
                                <td><?php echo categoryTicket($tickets->category); ?></td>
                                <td>
                                    <strong><?php echo e($tickets->title); ?></strong><br>
                                    <small class="text-muted">
                                        <?php echo Str::limit(strip_tags($tickets->body), 80); ?>

                                    </small>
                                </td>
                                <td><?php echo statusTicket($tickets->status); ?></td>
                                <td>
                                    <small><?php echo e($tickets->created_at->format('d/m/Y H:i')); ?></small><br>
                                    <small class="text-muted"><?php echo e($tickets->created_at->diffForHumans()); ?></small>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="ti ti-inbox fs-3"></i><br>
                                        Không có ticket nào
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($ticket->hasPages()): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($ticket->appends(request()->all())->links('pagination::bootstrap-4')); ?>

                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
<script>
    // Khởi tạo tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Src Khách\maxsocial\resources\views/admin/ticket/ticket.blade.php ENDPATH**/ ?>