<?php $__env->startSection('title', "Thêm mới sản phẩm con"); ?>
<?php $__env->startSection('content'); ?>
    <div class="card custom-card shadow">
        <div class="card-header">
            <h4 class="card-title">Thêm mới sản phẩm con</h4>
        </div>
        <div class="card-body">
            <form action="<?php echo e(route('admin.products.child.store')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="form-group mb-3">
                    <label for="product_main_id" class="form-label">Sản phẩm chính</label>
                    <select name="product_main_id" id="product_main_id" class="form-control">
                        <option value="">Chọn sản phẩm chính</option>
                        <?php $__currentLoopData = \App\Models\ProductMain::where('domain', request()->getHost())->where('status', 'active')->orderBy('order','asc')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productMain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($productMain->id); ?>" <?php echo e(old('product_main_id') == $productMain->id ? 'selected' : ''); ?>><?php echo e($productMain->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label for="name" class="form-label">Tên sản phẩm</label>
                    <input type="text" name="name" id="name" class="form-control" value="<?php echo e(old('name')); ?>">
                </div>
                <div class="form-group mb-3">
                    <label for="price" class="form-label">Giá sản phẩm</label>
                    <input type="text" name="price" id="price" class="form-control" value="<?php echo e(old('price')); ?>">
                </div>
                <div class="row">
                    <div class="col-md-6 form-group mb-3">
                        <label for="status" class="form-label">Trạng thái</label>
                        <select name="status" id="status" class="form-control">
                            <option value="active" <?php echo e(old('status') == 'active' ? 'selected' : ''); ?>>Kích hoạt</option>
                            <option value="inactive" <?php echo e(old('status') == 'inactive' ? 'selected' : ''); ?>>Chưa kích hoạt</option>
                        </select>
                    </div>
                    <div class="col-md-6 form-group mb-3">
                        <label for="order" class="form-label">Thứ tự</label>
                        <input type="number" min="0" name="order" id="order" class="form-control" value="<?php echo e(old('order')); ?>">
                    </div>
                    <div class="col-md-6 form-group mb-3">
                        <label for="type" class="form-label">Loại</label>
                        <select name="type" id="type" class="form-control">
                            <option value="manual" <?php echo e(old('type') == 'manual' ? 'selected' : ''); ?>>Thủ công</option>
                            <option value="taphoammo" <?php echo e(old('type') == 'taphoammo' ? 'selected' : ''); ?>>API: Taphoammo</option>
                        </select>
                    </div>
                    <div class="col-md-6 form-group mb-">
                        <label for="providerToken" class="form-label">Token sản phẩm (taphoammo)</label>
                        <input type="text" name="providerToken" id="providerToken" class="form-control" value="<?php echo e(old('providerToken')); ?>">
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Thêm mới</button>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/product/child-create.blade.php ENDPATH**/ ?>