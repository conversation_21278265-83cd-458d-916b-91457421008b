<?php $__env->startSection('title', 'Dòng Tiền'); ?>
<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <div class="card custom-card">
            <div class="card-header text-uppercase">
                <h5 class="card-title text-uppercase">Dòng Tiền</h5>
            </div>
            <div class="card-body">
                <form action="" method="GET">
                    <div class="row justify-item-center mb-3">
                        <div class="col-md-2 col-4 mb-2">
                            <label class="form-label">Hiển Thị</label>
                            <select name="per_page" id="per_page" class="form-select form-control" onchange="this.form.submit()">
                                <?php $__currentLoopData = [10, 25, 50, 100, 1000, 5000, 10000]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="">--Hi<PERSON>n Thị --</option>
                                <option value="<?php echo e($option); ?>" <?php echo e(request('per_page') == $option ? 'selected' : ''); ?>>
                                - <?php echo e($option); ?> -
                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-lg col-md-4 col-8 mb-2">
                            <label class="form-label">Loại Giao Dịch</label>
                            <select name="type" class="form-select form-controlsl2">
                                <option value="">--Tất Cả --</option>
                                <option value="recharge" <?php echo e(request('type') == 'recharge' ? 'selected' : ''); ?>>Nạp Tiền</option>
                                <option value="order" <?php echo e(request('type') == 'order' ? 'selected' : ''); ?>>Mua Đơn</option>
                                <option value="balance" <?php echo e(request('type') == 'balance' ? 'selected' : ''); ?>>Số Dư</option>
                                <option value="refund" <?php echo e(request('type') == 'refund' ? 'selected' : ''); ?>>Hoàn Tiền</option>
                                <option value="withdraw" <?php echo e(request('type') == 'withdraw' ? 'selected' : ''); ?>>Rút Tiền</option>
                                <option value="web_renew" <?php echo e(request('type') == 'web_renew' ? 'selected' : ''); ?>>Gia Hạn Site Con</option>
                            </select>
                        </div>
                        <div class="col-lg col-md-4 col-6 mb-2">
                            <label class="form-label">Từ Ngày</label>
                            <input type="date" name="start_date" class="form-control" value="<?php echo e(request('start_date')); ?>">
                        </div>
                        <div class="col-lg col-md-4 col-6 mb-2">
                            <label class="form-label">Đến Ngày</label>
                            <input type="date" name="end_date" class="form-control" value="<?php echo e(request('end_date')); ?>">
                        </div>
                        <div class="col-lg col-md-4 col-6 mb-2">
                            <label class="form-label">Người Dùng</label>
                            <input type="text" name="username" class="form-control" value="<?php echo e(request('username')); ?>" placeholder="Tên Người Dùng...">
                        </div>
                        <div class="col-lg col-md-4 col-6 mb-2">
                            <label class="form-label">Mã Giao Dịch</label>
                            <input type="text" name="tran_code" class="form-control" value="<?php echo e(request('tran_code')); ?>" placeholder="Mã Giao Dịch...">
                        </div>
                        <div class="col-lg col-md-4 col-12 mb-2">
                            <label class="form-label">Tìm Kiếm</label>
                            <div class=" input-group">
                                <input type="text" name="search" class="form-control" value="<?php echo e(request('search')); ?>" placeholder="Tìm Kiếm...">
                                <button type="submit" class="btn btn-primary"><i class="fad fa-search me-2"></i></button>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="table-responsive">
                    <table class="table table-vcenter table-default text-nowrap table-borderless table-mb w-100">
                        <thead class=" text-uppercase">
                            <tr>
                                <th>#</th>
                                <th>Người Dùng</th>
                                <th>Loại Giao Dịch</th>
                                <th>Mã Giao Dịch</th>
                                <th>Số Dư Trước</th>
                                <th>Số Dư Thay Đổi</th>
                                <th>Số Dư Cuối</th>
                                <th>Thời Gian</th>
                                <th>Ghi Chú</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if($transactions->isEmpty()): ?> 
                                <?php echo $__env->make('table-search-not-found', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> 
                            <?php endif; ?>                            
                            <?php $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($transaction->id); ?></td>
                                <td><?php echo e($transaction->user->username ?? 'Null'); ?></td>
                                <td>
                                    <?php if($transaction->type == 'recharge'): ?>
                                    <span class="badge bg-success">Nạp Tiền</span>
                                    <?php elseif($transaction->type == 'order'): ?>
                                    <span class="badge bg-primary">Đơn Hàng</span>
                                    <?php elseif($transaction->type == 'web_create'): ?>
                                    <span class="badge bg-success">Tạo Site Con</span>
                                    <?php elseif($transaction->type == 'web_renew'): ?>
                                    <span class="badge bg-warning">Gia Hạn Site Con</span>
                                    <?php elseif($transaction->type == 'balance'): ?>
                                    <span class="badge bg-info">Thay Đổi</span>
                                    <?php elseif($transaction->type == 'refund'): ?>
                                    <span class="badge bg-danger">Hoàn Tiền</span>
                                    <?php elseif($transaction->type == 'withdraw'): ?>
                                    <span class="badge bg-warning">Rút Tiền</span>
                                    <?php else: ?>
                                    <span class="badge bg-outline-danger shadow-danger"><?php echo e($transaction->type); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo e($transaction->tran_code); ?></span>
                                </td>
                                <td><?php echo e(number_format($transaction->before_balance)); ?>đ</td>
                                <td>
                                    <?php if($transaction->action == 'add'): ?>
                                    <p class="mb-0 text-success">
                                        +<?php echo e(number_format($transaction->first_balance)); ?>đ
                                    </p>
                                    <?php elseif($transaction->action == 'sub'): ?>
                                    <p class="mb-0 text-danger">
                                        -<?php echo e(number_format($transaction->first_balance)); ?>đ
                                    </p>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e(number_format($transaction->after_balance)); ?>đ</td>
                                <td><?php echo e($transaction->created_at); ?></td>
                                <td>
                                    <textarea type="text" class="form-control" rows="1" style="min-width: 400px" readonly><?php echo e($transaction->note); ?></textarea>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-center align-items-center mt-3 pagination-style-1">
                        <?php echo e($transactions->appends(request()->all())->links('pagination::bootstrap-4')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /home/<USER>/public_html/resources/views/admin/history/transactions.blade.php ENDPATH**/ ?>